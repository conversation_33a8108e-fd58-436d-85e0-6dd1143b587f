"""delete-profiles

Revision ID: ea16f6717343
Revises: 0a748416f7d4
Create Date: 2025-09-02 13:13:03.826032

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ea16f6717343"
down_revision = "0a748416f7d4"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "profile",
        sa.Column("deletion_timestamp", sa.DateTime(), nullable=True),
        schema="WebApp",
    )


def downgrade():
    op.drop_column("profile", "deletion_timestamp", schema="WebApp")
