services:
  run-migrations:
    build:
      context: ../dataset-manager
      dockerfile: Dockerfile
    volumes:
      - ../dataset-manager/alembic:/app/alembic
    init: true
    command: bash -c "alembic upgrade head"
    environment:
      - SQLALCHEMY_DATABASE_URL=mssql+pyodbc://indiebi:Password1!@sql-server/Shards?driver=ODBC+Driver+17+for+SQL+Server
    depends_on:
      sql-server:
        condition: service_healthy

  sql-server:
    image: crindiebimain.azurecr.io/cpt/local-sql-server-2022:master
    ports:
      - "1433:1433"
    healthcheck:
      test: /opt/mssql-tools18/bin/sqlcmd -C -S localhost -U sa -P "$$SA_PASSWORD" -Q "SELECT 1" || exit 1
      interval: 10s
      timeout: 3s
      retries: 10
      start_period: 10s

  azurite:
    image: mcr.microsoft.com/azure-storage/azurite:3.35.0
    hostname: azurite
    command: "azurite --blobHost 0.0.0.0 --queueHost 0.0.0.0 --tableHost 0.0.0.0 --skipApiVersionCheck"
    ports:
      - "10000:10000"
      - "10001:10001"
      - "10002:10002"
