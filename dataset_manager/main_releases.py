import asyncio

import sentry_sdk


def before_send_transaction(event, hint):
    # ignore errors logged by elasticapm logger
    if "elasticapm" in event.get("logger", ""):
        return None

    exception_values = event.get("exception", {}).get("values", [{}])[0]
    # ignore all exceptions defined in elasticapm package
    if "elasticapm" in exception_values.get("module", ""):
        return None

    # ignore ClientDisconnected exception triggered inside Elastic APM wrapper
    if "uvicorn" in exception_values.get(
        "module", ""
    ) and "ClientDisconnected" in exception_values.get("type", ""):
        return None

    # ignore health checks
    if event.get("type") == "transaction" and "/health" in event.get("transaction"):
        return None

    return event


sentry_sdk.init(
    traces_sample_rate=1.0,
    before_send_transaction=before_send_transaction,
)

import logging
from contextlib import asynccontextmanager
from functools import partial

from azure.storage.blob import BlobServiceClient
from elasticapm.contrib.starlette import ElasticAPM, make_apm_client
from fastapi import Depends, FastAPI, Request
from fastapi.openapi.utils import get_openapi

from dataset_manager.api import (
    exceptions,
    healthcheck,
)
from dataset_manager.api.api_key import require_key
from dataset_manager.azure.blob import ModelContainer
from dataset_manager.azure.client import auth_token_provider
from dataset_manager.azure.identity import credential
from dataset_manager.azure.management.capacities import get_capacieties_api
from dataset_manager.azure.powerbi.datasets import get_dataset_api
from dataset_manager.azure.powerbi.workspaces import get_workspace_api
from dataset_manager.config import (
    APP_VERSION,
    ELASTIC_SECRET_TOKEN,
    ELASTIC_SERVICE_URL,
    ENV,
    HEALTHCHECK_URL,
    SERVICE_NAME,
    get_config,
)
from dataset_manager.connectors.db_engine import SessionLocal
from dataset_manager.entities import (
    Environment,
    ProcessedAdlsCredentials,
    ProcessedAdlsURL,
    ServicePrincipalId,
)
from dataset_manager.jobs import infinite_executor
from dataset_manager.logic.release import (
    assign_releases,
    handle_requested_asign,
    handle_requested_shard,
)
from dataset_manager.logs import configure_logger
from dataset_manager.repo import get_repo
from dataset_manager.repo.capacity import CapacityState
from dataset_manager.repo.release import Release
from dataset_manager.tasks import repeat_every

configure_logger()
log = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    repeating_tasks = [
        assign_requested_releases(),
        handle_requested_assign_concurently(),
        handle_requested_shard_concurently(),
        check_if_all_capacities_are_unpaused(),
    ]

    asyncio.ensure_future(asyncio.gather(*repeating_tasks))

    yield


app = FastAPI(
    title="Dataset Manager",
    version=APP_VERSION,
    dependencies=[Depends(require_key)],
    lifespan=lifespan,
)

app.include_router(healthcheck.router)


@app.middleware("http")
async def handle_domain_exceptions(request: Request, call_next):
    return await exceptions.handle_domain_exceptions(request, call_next)


if ELASTIC_SECRET_TOKEN:
    # ElasticAPM middleware need to be added after HTTP Middleware
    # https://www.elastic.co/guide/en/apm/agent/python/current/starlette-support.html
    app.add_middleware(
        ElasticAPM,  # type: ignore[arg-type]
        client=make_apm_client(
            {
                "SERVICE_NAME": SERVICE_NAME,
                "SECRET_TOKEN": ELASTIC_SECRET_TOKEN,
                "SERVER_URL": ELASTIC_SERVICE_URL,
                "ENVIRONMENT": ENV,
                "TRANSACTION_IGNORE_URLS": [HEALTHCHECK_URL, f"{HEALTHCHECK_URL}/*"],
                "PROCESSORS": [
                    (
                        "dataset_manager.tracing.elastic_tracer"
                        ".remove_x_api_key_from_stacktrace_headers"
                    ),
                    "elasticapm.processors.sanitize_stacktrace_locals",
                    "elasticapm.processors.sanitize_http_request_cookies",
                    "elasticapm.processors.sanitize_http_headers",
                    "elasticapm.processors.sanitize_http_wsgi_env",
                    "elasticapm.processors.sanitize_http_request_body",
                ],
            }
        ),
    )


@app.get("/openapi.json", include_in_schema=False)
async def openapi():
    return get_openapi(title=app.title, version=app.version, routes=app.routes)


@repeat_every(seconds=10, wait_first=True, logger=log)
def assign_requested_releases():
    config = get_config()

    management_auth_token_provider = partial(
        auth_token_provider, config.AZURE_MNG_TOKEN_URL
    )
    capacieties_api = get_capacieties_api(
        config=config, auth_token_provider=management_auth_token_provider
    )

    with SessionLocal() as session, session.begin():
        repo = get_repo(session=session)
        assign_releases(repo=repo, capacieties_api=capacieties_api)


@repeat_every(seconds=20, wait_first=True, logger=log)
def handle_requested_assign_concurently():
    config = get_config()

    powerbi_auth_token_provider = partial(auth_token_provider, config.PBI_TOKEN_URL)
    workspace_api = get_workspace_api(auth_token_provider=powerbi_auth_token_provider)

    def _worker(release: Release):
        with SessionLocal() as session, session.begin():
            handle_requested_asign(
                repo=get_repo(session=session),
                workspace_api=workspace_api,
                environment=Environment(config.ENV_PREFIX + config.ENV),
                service_principal_id=ServicePrincipalId(config.SERVICE_PRINCIPAL_ID),
                release=release,
            )

    def _get_next_assign_jobs(limit, jobs_in_progress: list[Release]):
        with SessionLocal() as session, session.begin():
            return get_repo(session=session).release.requested_assign(
                limit=limit, ignore_uuids=[release.uuid for release in jobs_in_progress]
            )

    infinite_executor(
        wroker_fn=_worker,
        get_next_jobs_fn=_get_next_assign_jobs,
        max_paraller_jobs=5,
        thread_name_prefix="assign_worker",
    )


@repeat_every(seconds=20, wait_first=True, logger=log)
def handle_requested_shard_concurently():
    config = get_config()

    powerbi_auth_token_provider = partial(auth_token_provider, config.PBI_TOKEN_URL)
    workspace_api = get_workspace_api(auth_token_provider=powerbi_auth_token_provider)
    dataset_api = get_dataset_api(auth_token_provider=powerbi_auth_token_provider)
    model_container = ModelContainer(
        client=BlobServiceClient(
            account_url=config.STORAGE_ACCOUNT_URL, credential=credential
        )
    )

    def _worker(release: Release):
        with SessionLocal() as session, session.begin():
            handle_requested_shard(
                repo=get_repo(session=session),
                workspace_api=workspace_api,
                dataset_api=dataset_api,
                model_container=model_container,
                environment=Environment(config.ENV_PREFIX + config.ENV),
                processed_adls_url=ProcessedAdlsURL(config.PROCESSED_ADLS_URL),
                processed_adls_credentials=ProcessedAdlsCredentials(
                    config.PROCESSED_ADLS_CREDENTIALS
                ),
                release=release,
            )

    def _get_next_shard_jobs(limit, jobs_in_progress: list[Release]):
        with SessionLocal() as session, session.begin():
            return get_repo(session=session).release.requested_shard(
                limit=limit, ignore_uuids=[release.uuid for release in jobs_in_progress]
            )

    infinite_executor(
        wroker_fn=_worker,
        get_next_jobs_fn=_get_next_shard_jobs,
        max_paraller_jobs=10,
        thread_name_prefix="shard_worker",
    )


@repeat_every(seconds=300, wait_first=True, logger=log)
def check_if_all_capacities_are_unpaused():
    config = get_config()

    management_auth_token_provider = partial(
        auth_token_provider, config.AZURE_MNG_TOKEN_URL
    )
    capacieties_api = get_capacieties_api(
        config=config, auth_token_provider=management_auth_token_provider
    )
    with SessionLocal() as session, session.begin():
        repo = get_repo(session=session)
        all_capacities = repo.capacity.all()
        for cp in all_capacities:
            if cp.state != CapacityState.PAUSED:
                details = capacieties_api.details(cp.name)
                if details["properties"]["state"] == CapacityState.PAUSED:
                    log.error(f"Capacity {cp.name} should not be paused")
                    capacieties_api.resume(cp.name)
