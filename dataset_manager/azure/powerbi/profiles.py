from dataset_manager.azure.powerbi.service import request


def create(profile_name: str) -> str:
    profile = _get_by_name(profile_name)
    if profile:
        return profile[0]["id"]
    response = request(
        "post",
        "https://api.powerbi.com/v1.0/myorg/profiles",
        json={"displayName": profile_name},
    )
    data = response.json()
    return data["id"]


def delete(profile_id: str):
    request("delete", f"https://api.powerbi.com/v1.0/myorg/profiles/{profile_id}")


def _get_by_name(profile_name: str):
    response = request("get", "https://api.powerbi.com/v1.0/myorg/profiles")
    return [
        profile
        for profile in response.json()["value"]
        if profile["displayName"] == profile_name
    ]
