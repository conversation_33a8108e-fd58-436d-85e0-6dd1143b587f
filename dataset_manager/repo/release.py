from __future__ import annotations

import enum
import uuid
from datetime import datetime
from itertools import batched

from pydantic import <PERSON>Model, ConfigDict, field_serializer
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, func, text
from sqlalchemy.dialects.mssql import UNIQUEIDENTIFIER
from sqlalchemy.orm import Session
from sqlalchemy.sql.sqltypes import DateT<PERSON>, Enum, Integer, String

from dataset_manager.connectors.db_engine import Base, filter_query
from dataset_manager.entities import PermissionSetUUID, ReleaseUUID, StudioId
from dataset_manager.repo.profile import _DBProfile
from dataset_manager.repo.version import VersionId


class ReleaseStatus(str, enum.Enum):
    REQUESTED = "Requested"
    REQUESTED_SHARD = "RequestedShard"
    REQUESTED_ASSIGN = "RequestedAssign"
    DONE = "Done"
    OUTDATED = "Outdated"
    FAILED = "Failed"
    CANCELED = "Canceled"


class Release(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    uuid: ReleaseUUID
    studio_id: StudioId
    version_id: VersionId
    permission_set_uuid: PermissionSetUUID
    creation_timestamp: datetime
    status: ReleaseStatus
    is_full_recreate: bool
    try_count: int

    @field_serializer("creation_timestamp", when_used="unless-none")
    def serialize_creation_timestamp(self, creation_timestamp: datetime):
        return creation_timestamp.isoformat()


class CreateRelease(BaseModel):
    studio_id: int
    version_id: VersionId
    permission_set_uuid: uuid.UUID
    is_full_recreate: bool


class _DBRelease(Base):
    __tablename__ = "release"
    __table_args__ = {"schema": "WebApp"}
    _uuid = Column(
        "uuid",
        UNIQUEIDENTIFIER,
        nullable=False,
        unique=True,
        primary_key=True,
        server_default=func.NEWID(),
    )
    studio_id = Column("studio_id", Integer, nullable=False)
    version_id = Column("version_id", String, nullable=False)
    _permission_set_uuid = Column(
        "permission_set_uuid", UNIQUEIDENTIFIER, nullable=False
    )
    creation_timestamp = Column(
        "creation_timestamp", DateTime, nullable=False, default=datetime.utcnow
    )
    status = Column("status", Enum(ReleaseStatus), nullable=False)
    is_full_recreate = Column("is_full_recreate", Boolean, nullable=False)
    try_count = Column("try_count", Integer, nullable=False, server_default="0")

    @property
    def uuid(self):
        return self._uuid.lower()

    @uuid.setter
    def uuid(self, uuid):
        self._uuid = uuid

    @property
    def permission_set_uuid(self):
        return (
            self._permission_set_uuid.lower()
            if self._permission_set_uuid is not None
            else None
        )

    @permission_set_uuid.setter
    def permission_set_uuid(self, permission_set_uuid):
        self._permission_set_uuid = permission_set_uuid


class ReleaseNotFound(Exception):
    pass


class ReleaseRepo:
    def __init__(self, session: Session) -> None:
        self._session = session

    def create_bulk(self, new_releases: list[CreateRelease]) -> None:
        self._set_undone_releases_as_outdated(new_releases)

        orm_releases = []
        for new_release in new_releases:
            orm_release = _DBRelease(**new_release.model_dump())
            orm_release.status = ReleaseStatus.REQUESTED
            orm_release.try_count = 0
            orm_releases.append(orm_release)

        self._session.bulk_save_objects(orm_releases)
        self._session.flush()

    def create(self, new_release: CreateRelease) -> Release:
        self._set_undone_releases_as_outdated([new_release])

        orm_release = _DBRelease(**new_release.model_dump())
        orm_release.status = ReleaseStatus.REQUESTED
        orm_release.try_count = 0

        self._session.add(orm_release)
        self._session.flush()
        self._session.refresh(orm_release)

        return Release.model_validate(orm_release)

    def get(self, release_uuid: ReleaseUUID) -> Release:
        orm_release = filter_query(
            self._session, _DBRelease, uuid=release_uuid
        ).one_or_none()
        if orm_release is None:
            raise ReleaseNotFound
        return Release.model_validate(orm_release)

    def not_done(self) -> list[Release]:
        orm_releases = (
            self._session.query(_DBRelease)
            .outerjoin(_DBProfile, _DBRelease.studio_id == _DBProfile.studio_id)
            .order_by(
                text(
                    "CAST(CASE WHEN ([WebApp].profile.studio_id is null) THEN (0) ELSE (1) END AS BIT)"
                ),
                _DBRelease.creation_timestamp,
            )
            .filter(
                _DBRelease.status.in_(
                    [
                        ReleaseStatus.REQUESTED,
                        ReleaseStatus.REQUESTED_SHARD,
                        ReleaseStatus.REQUESTED_ASSIGN,
                    ]
                )
            )
            .all()
        )
        return [Release.model_validate(orm_release) for orm_release in orm_releases]

    def requested_assign(
        self, limit: int, ignore_uuids: list[ReleaseUUID] | None = None
    ) -> list[Release]:
        return self._search_for_status(
            status=ReleaseStatus.REQUESTED_ASSIGN,
            limit=limit,
            ignore_uuids=ignore_uuids,
        )

    def requested_shard(
        self, limit: int, ignore_uuids: list[ReleaseUUID] | None = None
    ) -> list[Release]:
        return self._search_for_status(
            status=ReleaseStatus.REQUESTED_SHARD,
            limit=limit,
            ignore_uuids=ignore_uuids,
        )

    def _search_for_status(
        self,
        status: ReleaseStatus,
        limit: int,
        ignore_uuids: list[ReleaseUUID] | None = None,
    ) -> list[Release]:
        orm_releases = (
            self._session.query(_DBRelease)
            .outerjoin(_DBProfile, _DBRelease.studio_id == _DBProfile.studio_id)
            .order_by(_DBRelease.creation_timestamp)
            .filter(_DBRelease.status == status)
        )
        if ignore_uuids:
            orm_releases = orm_releases.filter(
                _DBRelease._uuid.notin_([str(_uuid) for _uuid in ignore_uuids])
            )

        orm_releases = orm_releases.limit(limit)

        return [Release.model_validate(orm_release) for orm_release in orm_releases]

    def save(self, release: Release) -> Release:
        orm_release = self._session.get(_DBRelease, ident=release.uuid)
        assert orm_release is not None

        orm_release.status = release.status
        orm_release.try_count = release.try_count

        self._session.add(orm_release)
        self._session.flush()
        self._session.refresh(orm_release)

        return Release.model_validate(orm_release)

    def _set_undone_releases_as_outdated(
        self, new_releases: list[CreateRelease], sql_batch_size: int = 2000
    ) -> None:
        assert len(new_releases) > 0

        batches = batched(
            {new_release.studio_id for new_release in new_releases}, sql_batch_size
        )

        for batch in batches:
            self._session.query(_DBRelease).filter(
                _DBRelease.studio_id.in_(batch),
                _DBRelease.status == ReleaseStatus.REQUESTED,
            ).update(
                {_DBRelease.status: ReleaseStatus.OUTDATED}, synchronize_session=False
            )
