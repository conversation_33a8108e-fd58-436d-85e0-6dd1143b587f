from pydantic import BaseModel

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.api.dataset.dataset import (
    DBContextDependency,
    StudioIdDependency,
    router,
)
from dataset_manager.azure.powerbi.embed import (
    generate_simple_embed_token,
)
from dataset_manager.entities import BaseStr<PERSON>ield
from dataset_manager.repo.permission_set import PermissionSetRepo
from dataset_manager.repo.profile import ProfileRepo
from dataset_manager.repo.shard import ShardRepo
from dataset_manager.repo.version import VersionId, VersionRepo


class SecurityKey(BaseStrField):
    pass


class EmbedToken(BaseModel):
    token: str
    expiration_date: str
    dataset_id: str
    version: VersionId
    security_key_value: SecurityKey
    hidden_slicers: bool


@router.get(
    "/dashboards-auth-token",
    response_model=EmbedToken,
    tags=[ApiTag.PUBLIC],
)
@router.get(
    "/dashboards-auth-token/version/{requested_version}",
    response_model=EmbedToken,
    tags=[ApiTag.PUBLIC],
)
def get_dashboards_auth_token(
    studio_id: StudioIdDependency,
    db_context: DBContextDependency,
    requested_version: VersionId | None = None,
    lifetime_in_minutes: int = 0,
):
    with db_context() as session:
        profile = ProfileRepo(session=session).get_for_studio(studio_id=studio_id)

        version = VersionRepo(session=session).get(
            requested_version or profile.active_version_id
        )
        assert profile.permission_set_uuid is not None
        permission_set = PermissionSetRepo(session=session).get(
            uuid=profile.permission_set_uuid
        )
        shard = ShardRepo(session=session).get_for_permission_set_and_version(
            permission_set=permission_set,
            version=version,
        )

    token, expiration = generate_simple_embed_token(
        dataset_ids=[shard.dataset_id, version.visuals_dataset_id],
        reports_ids=version.visuals_reports_ids,
        lifetime_in_minutes=lifetime_in_minutes,
    )

    return EmbedToken(
        token=token,
        expiration_date=expiration,
        dataset_id=shard.dataset_id,
        version=version.id,
        security_key_value=SecurityKey(permission_set.permission_set_json),
        hidden_slicers=True,  # TODO: ask SaaS team if we can remove it
    )
