from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from dataset_manager.api.api_tags import ApiTag
from dataset_manager.azure.powerbi.profiles import delete
from dataset_manager.connectors.db_engine import get_db
from dataset_manager.connectors.user_service import (
    get_legacy_studio_id_from_user_id_sync,
)
from dataset_manager.repo import get_repo
from dataset_manager.repo.profile import Profile

router = APIRouter(prefix="/user")


@router.delete("/{user_id}", response_model=Profile, tags=[ApiTag.PRIVATE])
def delete_profile(
    user_id: str,
    session: Session = Depends(get_db),
):
    studio_id = get_legacy_studio_id_from_user_id_sync(user_id)
    profile = repo.profile.get_for_studio(studio_id=studio_id)
    delete(profile.profile_id)
    return repo.profile.delete(profile=profile)
