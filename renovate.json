{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base"], "rebaseWhen": "conflicted", "packageRules": [{"groupName": "non-prod-dependencies", "matchDepTypes": ["dev", "test"]}, {"groupName": "azure-libraries", "matchPackagePatterns": ["^azure-"]}, {"groupName": "light-dependencies", "matchPackageNames": ["fire", "logging-json", "types-python-dateutil", "requests", "requests-toolbelt", "httpx", "psutil", "python-multipart", "ecs-logging", "elastic-apm", "aiohttp", "pydantic", "pydantic-settings"]}]}