from functools import partial

from dataset_manager.azure.client import auth_token_provider
from dataset_manager.azure.powerbi.workspaces import get_workspace_api
from dataset_manager.config import get_config


def x_test_run():
    profile_id_tom = "63a0ba0e-a09c-43b4-b688-1c6720c40205"
    profile_id_860 = "e9d540e4-8227-4c63-bf09-cfdb2b23a219"
    config = get_config()

    dev_zero_workspace_id = "77db0171-7031-4919-b756-7e3072481549"

    powerbi_auth_token_provider = partial(auth_token_provider, config.PBI_TOKEN_URL)
    workspace_api = get_workspace_api(auth_token_provider=powerbi_auth_token_provider)
    workspace_api.assing_admins(
        workspace_id=dev_zero_workspace_id, admins=config.DEBUG_ADMINS
    )
    # users = workspace_api._get_users(
    #     workspace_id="63f77e5f-cec4-4377-8078-cbb2fcb6e7bd"
    # )
    # token_bez = generate_simple_embed_token(
    #     # profile_id="63a0ba0e-a09c-43b4-b688-1c6720c40205",
    #     dataset_ids=[
    #         "2d8407c2-d75c-4d4b-b70f-b841cf3aa561",
    #         "28ab1a17-78a3-4b03-a63c-67318b529460",
    #     ],
    #     reports_ids=[
    #         "cc88ec31-4b25-41de-a5e6-1e79bb61729f",
    #         "27a4cc86-3775-4d7d-9198-1f5f5a850c3e",
    #         "cc1d8167-d0b4-4205-9985-638879486067",
    #         "24adce07-808e-46e7-914c-2dd8b21f94d2",
    #         "bbf542aa-cec8-4fd9-b71c-f8ddd08eeea4",
    #         "36dbcf66-f1a2-453c-8a75-b187539749d5",
    #         "37c35e09-b3c6-4734-84da-43b4678ad090",
    #         "67884953-aca3-4e15-a9bf-9ff0e6f6903e",
    #         "31632ce5-ec78-4f6c-a821-47344bdc7139",
    #         "4397d910-e767-47ea-99a0-909286fcb0b0",
    #         "3fe32609-b857-4bf7-a55c-5a223a663014",
    #         "5b95a7f2-63be-4703-81f7-237d3f68b10c",
    #         "e2037acd-3c92-4118-94b5-d4b98b8c9415",
    #         "8d8a3139-88be-4d19-a848-4dfd7f4c4290",
    #         "dd84ce0e-7ab8-4b1e-afce-4420184e4f90",
    #         "87b2bfb1-7f19-420a-81cf-d82645553111",
    #         "30c15846-7f48-494f-89e2-5edb0d228f30",
    #     ],
    # )
    # print("bez: ", token_bez)

    # token = generate_embed_token(
    #     profile_id=profile_id_860,
    #     dataset_ids=[
    #         "2d8407c2-d75c-4d4b-b70f-b841cf3aa561",
    #         "28ab1a17-78a3-4b03-a63c-67318b529460",
    #     ],
    #     reports_ids=[
    #         "cc88ec31-4b25-41de-a5e6-1e79bb61729f",
    #         "27a4cc86-3775-4d7d-9198-1f5f5a850c3e",
    #         "cc1d8167-d0b4-4205-9985-638879486067",
    #         "24adce07-808e-46e7-914c-2dd8b21f94d2",
    #         "bbf542aa-cec8-4fd9-b71c-f8ddd08eeea4",
    #         "36dbcf66-f1a2-453c-8a75-b187539749d5",
    #         "37c35e09-b3c6-4734-84da-43b4678ad090",
    #         "67884953-aca3-4e15-a9bf-9ff0e6f6903e",
    #         "31632ce5-ec78-4f6c-a821-47344bdc7139",
    #         "4397d910-e767-47ea-99a0-909286fcb0b0",
    #         "3fe32609-b857-4bf7-a55c-5a223a663014",
    #         "5b95a7f2-63be-4703-81f7-237d3f68b10c",
    #         "e2037acd-3c92-4118-94b5-d4b98b8c9415",
    #         "8d8a3139-88be-4d19-a848-4dfd7f4c4290",
    #         "dd84ce0e-7ab8-4b1e-afce-4420184e4f90",
    #         "87b2bfb1-7f19-420a-81cf-d82645553111",
    #         "30c15846-7f48-494f-89e2-5edb0d228f30",
    #     ],
    # )
    # print("z: ", token)
    # x = workspace_api._get_by_name("prod_3.8.1_f7c1f235-7e22-405c-97be-c51fc69f815a")
    # print("workspcace_id: ", x)

    # print("Adding user to visuals workspace")
    # visuals_workspace = "49728FD4-30D5-48E5-93A5-81FE886C8333"
    # workspace_api._assign_user_to_workspace(
    #     visuals_workspace,
    #     user={
    #         "groupUserAccessRight": "Admin",
    #         "displayName": "prod_311",
    #         "identifier": "0fc74812-2aca-450c-8668-c3e9f61adaf5",
    #         "principalType": "App",
    #         "profile": {"id": "63a0ba0e-a09c-43b4-b688-1c6720c40205"},
    #     },
    # )
    # print(x)
