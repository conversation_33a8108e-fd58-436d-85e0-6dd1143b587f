import io
import json
from datetime import datetime
from zipfile import ZIP_DEFLATED, ZipFile

import pytest
import responses
from azure.storage.blob import ContainerClient
from freezegun import freeze_time
from responses import matchers
from sqlalchemy.orm import Session
from starlette.status import HTTP_200_OK, HTTP_422_UNPROCESSABLE_ENTITY

from dataset_manager.config import get_config
from dataset_manager.main import app
from dataset_manager.repo.release import ReleaseStatus, _DBRelease
from dataset_manager.repo.version import _DBVersion


@pytest.fixture
def create_not_existing_workspace_dataset_template(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups",
        json={"value": []},
        match=[
            matchers.query_param_matcher({"$filter": "name eq 'dev_2.2.0_template'"})
        ],
    )
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups",
        json={
            "id": "dataset_template-4e18-aea3-4cb4a3a50b48",
            "isReadOnly": False,
            "isOnDedicatedCapacity": False,
            "name": "dev_2.2.0_template",
        },
        match=[matchers.json_params_matcher({"name": "dev_2.2.0_template"})],
    )


@pytest.fixture
def create_not_existing_dev_workspace_dataset_template(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups",
        json={"value": []},
        match=[
            matchers.query_param_matcher(
                {"$filter": "name eq 'dev_2.2.0_dev_template'"}
            )
        ],
    )
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups",
        json={
            "id": "dataset_template-4e18-aea3-4cb4a3a50b48",
            "isReadOnly": False,
            "isOnDedicatedCapacity": False,
            "name": "dev_2.2.0_dev_template",
        },
        match=[matchers.json_params_matcher({"name": "dev_2.2.0_dev_template"})],
    )


@pytest.fixture
def deploy_dataset(responses_mock: responses.RequestsMock):
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/dataset_template-4e18-aea3-4cb4a3a50b48/imports?datasetDisplayName=Dataset&nameConflict=CreateOrOverwrite",
        match=[matchers.multipart_matcher({"value": b"SOME_DATASET"})],
        json={"id": "dataset-deploy-e247-4d83-ae5a-014028cb0665"},
    )


@pytest.fixture
def get_uploaded_dataset_id_with_retry(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/dataset_template-4e18-aea3-4cb4a3a50b48/imports/dataset-deploy-e247-4d83-ae5a-014028cb0665",
        json={
            "id": "dataset-deploy-e247-4d83-ae5a-014028cb0665",
            "importState": "Publishing",
            "createdDateTime": "2018-05-08T14:56:18.477Z",
            "updatedDateTime": "2018-05-08T14:56:18.477Z",
            "name": "v2",
            "connectionType": "import",
            "source": "Upload",
            "datasets": [],
            "reports": [],
        },
    )
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/dataset_template-4e18-aea3-4cb4a3a50b48/imports/dataset-deploy-e247-4d83-ae5a-014028cb0665",
        json={
            "id": "dataset-deploy-e247-4d83-ae5a-014028cb0665",
            "importState": "Succeeded",
            "createdDateTime": "2018-05-08T14:56:18.477Z",
            "updatedDateTime": "2018-05-08T14:56:18.477Z",
            "name": "v2",
            "connectionType": "import",
            "source": "Upload",
            "datasets": [
                {
                    "id": "dataset_template_8037-4d0c-896e-a46fb27",
                    "name": "Dataset",
                    "webUrl": "https://app.powerbi.com/groups/dataset_template-4e18-aea3-4cb4a3a50b48/datasets/cfafbeb1-8037-4d0c-896e-a46fb27ff229",
                }
            ],
            "reports": [],
        },
    )


@pytest.fixture
def create_not_existing_visuals_workspace(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups",
        json={"value": []},
        match=[
            matchers.query_param_matcher({"$filter": "name eq 'dev_2.2.0_visuals'"})
        ],
    )
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups",
        json={
            "id": "visuals-4e18-aea3-4cb4a3a50b48",
            "isReadOnly": False,
            "isOnDedicatedCapacity": False,
            "name": "dev_2.2.0_visuals",
        },
        match=[matchers.json_params_matcher({"name": "dev_2.2.0_visuals"})],
    )


@pytest.fixture
def create_not_existing_dev_visuals_workspace(
    responses_mock: responses.RequestsMock,
):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups",
        json={"value": []},
        match=[
            matchers.query_param_matcher({"$filter": "name eq 'dev_2.2.0_dev_visuals'"})
        ],
    )
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups",
        json={
            "id": "visuals-4e18-aea3-4cb4a3a50b48",
            "isReadOnly": False,
            "isOnDedicatedCapacity": False,
            "name": "dev_2.2.0_dev_visuals",
        },
        match=[matchers.json_params_matcher({"name": "dev_2.2.0_dev_visuals"})],
    )


@pytest.fixture
def deploy_first_visual_with_replaced_connection_string(
    responses_mock: responses.RequestsMock,
):
    expected_visual_pbix = io.BytesIO()
    with (
        freeze_time("2022-11-07 10:00"),
        ZipFile(expected_visual_pbix, "w", compression=ZIP_DEFLATED) as zip_file,
    ):
        visuals_dataset_id = "dataset_template_8037-4d0c-896e-a46fb27"
        connection_string = {
            "Version": 3,
            "Connections": [
                {
                    "Name": "EntityDataSource",
                    "ConnectionString": f'Data Source=pbiazure://api.powerbi.com;Initial Catalog={visuals_dataset_id};Identity Provider="https://login.microsoftonline.com/common, https://analysis.windows.net/powerbi/api, 7f67af8a-fedc-4b08-8b4e-37c4d127b6cf";Integrated Security=ClaimsToken',
                    "ConnectionType": "pbiServiceLive",
                    "PbiServiceModelId": 0,
                    "PbiModelVirtualServerName": "sobe_wowvirtualserver",
                    "PbiModelDatabaseName": visuals_dataset_id,
                }
            ],
        }
        zip_file.writestr("charts", "Dashboards")
        zip_file.writestr("Connections", json.dumps(connection_string))

    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/visuals-4e18-aea3-4cb4a3a50b48/imports?datasetDisplayName=Dashboards&nameConflict=CreateOrOverwrite",
        match=[matchers.multipart_matcher({"value": expected_visual_pbix.getvalue()})],
        json={"id": "first-visual-deploy-e247-4d83-ae5a-014028cb0665"},
    )


@pytest.fixture
def deploy_second_visual_with_replaced_connection_string(
    responses_mock: responses.RequestsMock,
):
    expected_visual_pbix = io.BytesIO()
    with (
        freeze_time("2022-11-07 10:00"),
        ZipFile(expected_visual_pbix, "w", compression=ZIP_DEFLATED) as zip_file,
    ):
        visuals_dataset_id = "dataset_template_8037-4d0c-896e-a46fb27"
        connection_string = {
            "Version": 3,
            "Connections": [
                {
                    "Name": "EntityDataSource",
                    "ConnectionString": f'Data Source=pbiazure://api.powerbi.com;Initial Catalog={visuals_dataset_id};Identity Provider="https://login.microsoftonline.com/common, https://analysis.windows.net/powerbi/api, 7f67af8a-fedc-4b08-8b4e-37c4d127b6cf";Integrated Security=ClaimsToken',
                    "ConnectionType": "pbiServiceLive",
                    "PbiServiceModelId": 0,
                    "PbiModelVirtualServerName": "sobe_wowvirtualserver",
                    "PbiModelDatabaseName": visuals_dataset_id,
                }
            ],
        }
        zip_file.writestr("charts", "Sales And Discounts")
        zip_file.writestr("Connections", json.dumps(connection_string))

    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/visuals-4e18-aea3-4cb4a3a50b48/imports?datasetDisplayName=Sales%20And%20Discounts&nameConflict=CreateOrOverwrite",
        match=[matchers.multipart_matcher({"value": expected_visual_pbix.getvalue()})],
        json={"id": "second-visual-deploy-e247-4d83-ae5a-014028cb0665"},
    )


@pytest.fixture
def get_deployed_reports(responses_mock: responses.RequestsMock):
    responses_mock.get(
        "https://api.powerbi.com/v1.0/myorg/groups/visuals-4e18-aea3-4cb4a3a50b48/reports",
        json={
            "value": [
                {
                    "id": "dashboards-9a9e3717-e1a2-4c3f-9cf2-97101e3af4aa",
                    "reportType": "PowerBIReport",
                    "name": "Dashboards",
                    "webUrl": "https://app.powerbi.com/groups/7b3e6789-dfb4-4357-8d30-2d5713d8d00d/reports/9a9e3717-e1a2-4c3f-9cf2-97101e3af4aa",
                    "embedUrl": "https://app.powerbi.com/reportEmbed?reportId=9a9e3717-e1a2-4c3f-9cf2-97101e3af4aa&groupId=7b3e6789-dfb4-4357-8d30-2d5713d8d00d&w=2&config=eyJjbHVzdGVyVXJsIjoiaHR0cHM6Ly9XQUJJLVVTLUNFTlRSQUwtQS1QUklNQVJZLXJlZGlyZWN0LmFuYWx5c2lzLndpbmRvd3MubmV0IiwiZW1iZWRGZWF0dXJlcyI6eyJtb2Rlcm5FbWJlZCI6dHJ1ZSwidXNhZ2VNZXRyaWNzVk5leHQiOnRydWUsInNraXBRdWVyeURhdGFTYWFTRW1iZWQiOnRydWUsInNraXBRdWVyeURhdGFQYWFTRW1iZWQiOnRydWUsInNraXBRdWVyeURhdGFFeHBvcnRUbyI6dHJ1ZX19",
                    "isFromPbix": True,
                    "isOwnedByMe": True,
                    "datasetId": "dataset_template_8037-4d0c-896e-a46fb27",
                    "datasetWorkspaceId": "7b3e6789-dfb4-4357-8d30-2d5713d8d00d",
                    "users": [],
                    "subscriptions": [],
                },
                {
                    "id": "sales-8501b756-a4b5-4723-8a4a-5363bb8aa4bd",
                    "reportType": "PowerBIReport",
                    "name": "Sales And Discounts",
                    "webUrl": "https://app.powerbi.com/groups/7b3e6789-dfb4-4357-8d30-2d5713d8d00d/reports/8501b756-a4b5-4723-8a4a-5363bb8aa4bd",
                    "embedUrl": "https://app.powerbi.com/reportEmbed?reportId=8501b756-a4b5-4723-8a4a-5363bb8aa4bd&groupId=7b3e6789-dfb4-4357-8d30-2d5713d8d00d&w=2&config=eyJjbHVzdGVyVXJsIjoiaHR0cHM6Ly9XQUJJLVVTLUNFTlRSQUwtQS1QUklNQVJZLXJlZGlyZWN0LmFuYWx5c2lzLndpbmRvd3MubmV0IiwiZW1iZWRGZWF0dXJlcyI6eyJtb2Rlcm5FbWJlZCI6dHJ1ZSwidXNhZ2VNZXRyaWNzVk5leHQiOnRydWUsInNraXBRdWVyeURhdGFTYWFTRW1iZWQiOnRydWUsInNraXBRdWVyeURhdGFQYWFTRW1iZWQiOnRydWUsInNraXBRdWVyeURhdGFFeHBvcnRUbyI6dHJ1ZX19",
                    "isFromPbix": True,
                    "isOwnedByMe": True,
                    "datasetId": "dataset_template_8037-4d0c-896e-a46fb27",
                    "datasetWorkspaceId": "7b3e6789-dfb4-4357-8d30-2d5713d8d00d",
                    "users": [],
                    "subscriptions": [],
                },
            ]
        },
    )


@pytest.fixture
def expected_desktop_definition():
    return {
        "version": "2.2.0",
        "last_updated": "2022-11-07T10:00:00",
        "defaults": {
            "item": "d0e810b6-7cf8-4e7d-a3bb-65e659eab772",
            "filters": {
                "date_range": "last 3 months",
                "games": "",
                "platforms": "",
                "geo": "",
                "tag": "",
            },
            "selected_measures": {
                "Revenue": "Revenue - Gross, less of Returns",
                "Units": "Units - Sold Directly, less of Returns",
                "Steam Wishlists": "Steam - Wishlist Adds",
                "Steam Visibility": "Steam - Non-owner Visits",
            },
        },
        "measure_groups": [
            {
                "id": "Revenue",
                "display_name": "Revenue",
                "measures": [
                    {
                        "id": "Revenue - Gross",
                        "internal_name": "Revenue - Gross",
                        "display_name": "Gross revenue",
                    },
                    {
                        "id": "Revenue - Returns & Chargebacks",
                        "internal_name": "Revenue - Returns & Chargebacks",
                        "display_name": "Returns and chargebacks",
                    },
                    {
                        "id": "Revenue - Gross, less of Returns",
                        "internal_name": "Revenue - Gross, less of Returns",
                        "display_name": "Gross, less of returns",
                    },
                    {
                        "id": "Revenue - Approx. Net",
                        "internal_name": "Revenue - Approx. Net",
                        "display_name": "Approximate net revenue",
                    },
                ],
            },
            {
                "id": "Units",
                "display_name": "Units",
                "measures": [
                    {
                        "id": "Units - Total Distributed",
                        "internal_name": "Units - Total Distributed",
                        "display_name": "Total units, including free",
                    },
                    {
                        "id": "Units - Free Units",
                        "internal_name": "Units - Free Units",
                        "display_name": "Free units",
                    },
                    {
                        "id": "Units - Activated Retail Copies",
                        "internal_name": "Units - Activated Retail Copies",
                        "display_name": "Retail activations",
                    },
                    {
                        "id": "Units - Returns & Chargebacks",
                        "internal_name": "Units - Returns & Chargebacks",
                        "display_name": "Returns and chargebacks",
                    },
                    {
                        "id": "Units - Sold Directly, less of Returns",
                        "internal_name": "Units - Sold Directly, less of Returns",
                        "display_name": "Sold directly, less of returns",
                    },
                ],
            },
            {
                "id": "Steam Wishlists",
                "display_name": "Steam Wishlists",
                "measures": [
                    {
                        "id": "Steam - Wishlist Adds",
                        "internal_name": "Steam - Wishlist Adds",
                        "display_name": "Wishlist additions",
                    },
                    {
                        "id": "Steam - Wishlist Activations",
                        "internal_name": "Steam - Wishlist Activations",
                        "display_name": "Wishlist activations",
                    },
                    {
                        "id": "Steam - Wishlist Deletes",
                        "internal_name": "Steam - Wishlist Deletes",
                        "display_name": "Wishlist deletes",
                    },
                ],
            },
            {
                "id": "Steam Visibility",
                "display_name": "Steam Visibility",
                "measures": [
                    {
                        "id": "Steam - Non-owner Impressions",
                        "internal_name": "Steam - Non-owner Impressions",
                        "display_name": "Non-owner Impressions",
                    },
                    {
                        "id": "Steam - Non-owner Visits",
                        "internal_name": "Steam - Non-owner Visits",
                        "display_name": "Non-owner Visits",
                    },
                ],
            },
        ],
        "menu_items": [
            {
                "id": "f14c4a93-61f5-4cb5-b6af-073048b8e600",
                "icon": "view-dashboard-outline",
                "type": "folder",
                "display_name": "Overview",
                "description": {
                    "title": "Overview",
                    "description": "A summarized view of your most relevant metrics like revenue, units sold, along with Steam wishlists and visibility. Here you can view your performance over time by title, store and geography.",
                    "features": [
                        "See performance over time by title, platform and geography",
                        "Identify strange dips and anomalies",
                        "Visualize the effectiveness of your discounts",
                        "Track your wishlists, visibility and click-through rates on Steam",
                    ],
                },
                "children": [
                    {
                        "id": "d0e810b6-7cf8-4e7d-a3bb-65e659eab772",
                        "type": "powerbi",
                        "icon": "view-dashboard-outline",
                        "display_name": "Main",
                        "internal_name": "CEO",
                        "pbix_name": "CEO",
                        "page_name": "ReportSectiond4e03815e262394d0e4a",
                        "description": {
                            "title": "Main",
                            "description": "Summary of your key revenue and unit metrics.",
                            "features": [
                                "Explore performance over time by title, platform and geography.",
                                "Check promo share and the effectiveness of your discouning events.",
                                "Analyze which games are most frequently purchased from wishlists.",
                            ],
                        },
                        "dashboard_url": "",
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Date",
                            "Selected Measure Revenue",
                            "Selected Measure Units",
                            "Studio",
                        ],
                        "internalPages": [
                            {
                                "display_name": "By date",
                                "page_name": "0343785ed11af63ee543",
                                "slicers": [
                                    "ProductName",
                                    "Stores",
                                    "Date",
                                    "Selected Measure Revenue",
                                    "Selected Measure Units",
                                    "Studio",
                                ],
                            },
                            {
                                "display_name": "By portal",
                                "page_name": "ReportSection4fd9d0cd5d9cf15114b8",
                                "slicers": [
                                    "ProductName",
                                    "Stores",
                                    "Date",
                                    "Selected Measure Revenue",
                                    "Selected Measure Units",
                                    "Studio",
                                ],
                            },
                            {
                                "display_name": "By product",
                                "page_name": "ReportSection9cb77ca956c882b0c14c",
                                "slicers": [
                                    "ProductName",
                                    "Stores",
                                    "Date",
                                    "Selected Measure Revenue",
                                    "Selected Measure Units",
                                    "Studio",
                                ],
                            },
                            {
                                "display_name": "By promo",
                                "page_name": "ReportSection309934420716dbd4ec7c",
                                "slicers": [
                                    "ProductName",
                                    "Stores",
                                    "Date",
                                    "Selected Measure Revenue",
                                    "Selected Measure Units",
                                    "Studio",
                                ],
                            },
                            {
                                "display_name": "By wishlists",
                                "page_name": "ReportSection4bd6520955aff4a497c0",
                                "slicers": [
                                    "ProductName",
                                    "Stores",
                                    "Date",
                                    "Selected Measure Revenue",
                                    "Selected Measure Units Excl Returns",
                                    "Studio",
                                ],
                            },
                            {
                                "display_name": "By country",
                                "page_name": "aaa737c1b197f7005f34",
                                "slicers": [
                                    "ProductName",
                                    "Stores",
                                    "Date",
                                    "Selected Measure Revenue",
                                    "Selected Measure Units",
                                    "Studio",
                                ],
                            },
                        ],
                        "is_new": True,
                    },
                    {
                        "id": "3f43f27c-5a40-48ab-8115-db018b3a030b",
                        "type": "powerbi",
                        "icon": "view-dashboard-outline",
                        "display_name": "By revenue",
                        "internal_name": "Dashboard",
                        "pbix_name": "Dashboards",
                        "page_name": "ReportSectiond4e03815e262394d0e4a",
                        "visual_container_id": "6ccabd080adb09e2b7e2",
                        "description": {
                            "title": "Revenue overview",
                            "description": "Overview of your revenue and other relevant metrics.",
                            "features": [
                                "See performance over time by title, platform and geography",
                                "Identify strange dips and anomalies",
                                "Visualize the effectiveness of your discounts",
                            ],
                        },
                        "dashboard_url": "https://app.powerbi.com/reportEmbed?reportId=dashboards-9a9e3717-e1a2-4c3f-9cf2-97101e3af4aa&groupId=visuals-4e18-aea3-4cb4a3a50b48&language=en&formatLocale=en-US&config=eyJjbHVzdGVyVXJsIjoiaHR0cHM6Ly93YWJpLXdlc3QtZXVyb3BlLWUtcHJpbWFyeS1yZWRpcmVjdC5hbmFseXNpcy53aW5kb3dzLm5ldCIsImVtYmVkRmVhdHVyZXMiOnsidXNhZ2VNZXRyaWNzVk5leHQiOnRydWV9LCJhbGciOiJIUzI1NiJ9.e30.DniugsEtOvpzMG9XosDAjacUkHQY0s4_vwSG6QqUR3c",
                        "accepts_measures": ["Revenue"],
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Date",
                            "Selected Measure Revenue",
                            "Studio",
                        ],
                    },
                    {
                        "id": "3c26b8af-08fd-4191-b779-7528db1ed911",
                        "type": "powerbi",
                        "icon": "view-dashboard-outline",
                        "display_name": "By units",
                        "internal_name": "Units Dashboard",
                        "pbix_name": "Dashboards",
                        "page_name": "ReportSection4fd9d0cd5d9cf15114b8",
                        "description": {
                            "title": "Units overview",
                            "description": "Overview of your units and other relevant metrics.",
                            "features": [
                                "See performance over time by title, platform and geography",
                                "Identify strange dips and anomalies",
                                "Visualize the effectiveness of your discounts",
                            ],
                        },
                        "dashboard_url": "https://app.powerbi.com/reportEmbed?reportId=dashboards-9a9e3717-e1a2-4c3f-9cf2-97101e3af4aa&groupId=visuals-4e18-aea3-4cb4a3a50b48&language=en&formatLocale=en-US&config=eyJjbHVzdGVyVXJsIjoiaHR0cHM6Ly93YWJpLXdlc3QtZXVyb3BlLWUtcHJpbWFyeS1yZWRpcmVjdC5hbmFseXNpcy53aW5kb3dzLm5ldCIsImVtYmVkRmVhdHVyZXMiOnsidXNhZ2VNZXRyaWNzVk5leHQiOnRydWV9LCJhbGciOiJIUzI1NiJ9.e30.DniugsEtOvpzMG9XosDAjacUkHQY0s4_vwSG6QqUR3c",
                        "accepts_measures": ["Units"],
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Date",
                            "Selected Measure Units",
                            "Studio",
                        ],
                    },
                    {
                        "id": "d8ca1e49-f037-4f85-899a-fbc7846b9436",
                        "type": "powerbi",
                        "icon": "view-dashboard-outline",
                        "display_name": "By visibility",
                        "internal_name": "Visibility Dashboard",
                        "pbix_name": "Dashboards",
                        "page_name": "ReportSection309934420716dbd4ec7c",
                        "description": {
                            "title": "Steam visibility overview",
                            "description": "Overview of your games' visibility and click-through rates on Steam.",
                            "features": [
                                "Dig deep into the sources and quality of your traffic",
                                "See how your engagement metrics change during sales, after you change your store art, or when you ship a big update",
                                "Better understand the causes behind specific sales over- and under-performing",
                                "Identify which discounts got major homepage boosts, and which were driven by wishlist activations and top charts",
                            ],
                        },
                        "dashboard_url": "https://app.powerbi.com/reportEmbed?reportId=dashboards-9a9e3717-e1a2-4c3f-9cf2-97101e3af4aa&groupId=visuals-4e18-aea3-4cb4a3a50b48&language=en&formatLocale=en-US&config=eyJjbHVzdGVyVXJsIjoiaHR0cHM6Ly93YWJpLXdlc3QtZXVyb3BlLWUtcHJpbWFyeS1yZWRpcmVjdC5hbmFseXNpcy53aW5kb3dzLm5ldCIsImVtYmVkRmVhdHVyZXMiOnsidXNhZ2VNZXRyaWNzVk5leHQiOnRydWV9LCJhbGciOiJIUzI1NiJ9.e30.DniugsEtOvpzMG9XosDAjacUkHQY0s4_vwSG6QqUR3c",
                        "accepts_measures": ["Steam Visibility"],
                        "limit_filters": {"platforms": ["Steam"]},
                        "disable_save": ["platforms"],
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Date",
                            "Selected Measure Visibility",
                            "Studio",
                        ],
                    },
                ],
            },
            {
                "id": "70a0ed66-f7d8-41ba-8974-0315603df230",
                "type": "powerbi",
                "icon": "chart-bar-stacked",
                "display_name": "Your sales",
                "internal_name": "Sales and Discounts",
                "pbix_name": "Sales And Discounts",
                "page_name": "ReportSectionf513e7352f0c0f119c5b",
                "visual_container_id": "b3a30e5086caa0fa651a",
                "description": {
                    "title": "Breakdown of your sales",
                    "description": "A simple time chart of all of your revenue and units sold with your discounting history broken down per store.",
                    "features": [
                        "Compare how different discounts work on different platforms",
                        "See which platforms are performing best and identify trends at a glance",
                    ],
                },
                "dashboard_url": "https://app.powerbi.com/reportEmbed?reportId=sales-8501b756-a4b5-4723-8a4a-5363bb8aa4bd&groupId=visuals-4e18-aea3-4cb4a3a50b48&language=en&formatLocale=en-US&config=eyJjbHVzdGVyVXJsIjoiaHR0cHM6Ly93YWJpLXdlc3QtZXVyb3BlLWUtcHJpbWFyeS1yZWRpcmVjdC5hbmFseXNpcy53aW5kb3dzLm5ldCIsImVtYmVkRmVhdHVyZXMiOnsidXNhZ2VNZXRyaWNzVk5leHQiOnRydWV9LCJhbGciOiJIUzI1NiJ9.e30.DniugsEtOvpzMG9XosDAjacUkHQY0s4_vwSG6QqUR3c",
                "accepts_measures": ["Revenue", "Units"],
                "slicers": [
                    "ProductName",
                    "Stores",
                    "Date",
                    "Selected Measure All",
                    "Sale Day Type",
                    "Studio",
                ],
            },
            {
                "id": "7c442eb3-add0-4060-a326-4fd6b3bc5b1c",
                "type": "powerbi",
                "icon": "heart-outline",
                "display_name": "Wishlists",
                "internal_name": "Wishlists",
                "pbix_name": "Steam Wishlists",
                "page_name": "ReportSection4fd9d0cd5d9cf15114b8",
                "visual_container_id": "7fed83f641ce21532268",
                "description": {
                    "title": "Wishlist analytics",
                    "description": "A view to track your wishlists metrics.",
                    "features": [
                        "See when your games were added to wishlists most often",
                        "Track how the number of additions, activations and deletions evolved over time",
                        "See which games are purchased most often from wishlists and how your wishlist balance is shaping up",
                    ],
                },
                "dashboard_url": "",
                "accepts_measures": ["Steam Wishlists"],
                "limit_filters": {
                    "platforms": [
                        "Steam",
                        "PlayStation America",
                        "PlayStation Asia",
                        "PlayStation Europe",
                        "PlayStation Japan",
                        "Nintendo Switch Japan",
                        "Nintendo Switch Taiwan/Hong Kong",
                        "Nintendo Switch Americas",
                        "Nintendo Switch Europe/Australia",
                        "Nintendo Switch Korea",
                        "Nintendo Switch China",
                        "Nintendo Switch Unknown",
                        "Nintendo Switch 2 Japan",
                        "Nintendo Switch 2 Taiwan/Hong Kong",
                        "Nintendo Switch 2 Americas",
                        "Nintendo Switch 2 Europe/Australia",
                        "Nintendo Switch 2 Korea",
                        "Nintendo Switch 2 China",
                        "Nintendo Switch 2 Unknown",
                        "Nintendo Other Japan",
                        "Nintendo Other Taiwan/Hong Kong",
                        "Nintendo Other Americas",
                        "Nintendo Other Europe/Australia",
                        "Nintendo Other Korea",
                        "Nintendo Other China",
                        "Nintendo Other Unknown",
                        "Nintendo Switch Asia",
                        "Nintendo Switch America",
                        "Nintendo Switch Europe",
                    ]
                },
                "disable_save": ["platforms"],
                "slicers": ["ProductName", "Stores", "Date", "Sale Day Type", "Studio"],
            },
            {
                "id": "a9ee4777-a739-46a7-83fe-677bb386f112",
                "type": "powerbi",
                "icon": "store-outline",
                "display_name": "Retail activations",
                "internal_name": "Retail activations",
                "pbix_name": "Steam Key Activations",
                "page_name": "ReportSection4fd9d0cd5d9cf15114b8",
                "visual_container_id": "2b4edff79350216553ae",
                "description": {
                    "title": "Retail activations",
                    "description": "A view to track your activation rates and locations for all of your retail keys.",
                    "features": [
                        "See the actual activation time for your bundle giveaways",
                        "Track which countries and regions activate the most keys purchased in local online stores",
                        "Get a better sense of the impact your retail strategy has on your direct sales",
                    ],
                },
                "dashboard_url": "",
                "limit_filters": {"platforms": ["Steam"]},
                "disable_save": ["platforms"],
                "slicers": ["ProductName", "Date", "Sale Day Type", "Studio"],
            },
            {
                "id": "bf4e9460-287e-4ea0-9691-0c8a7dcb7399",
                "type": "powerbi",
                "icon": "earth",
                "display_name": "Country data",
                "internal_name": "Country Data",
                "pbix_name": "Country Data",
                "page_name": "ReportSection4fd9d0cd5d9cf15114b8",
                "visual_container_id": "4e91702f0e8a2955c0da",
                "description": {
                    "title": "Country data table",
                    "description": "A detailed view with region-specific charts and a comprehensive table displaying core metrics by country and region.",
                    "features": [
                        "See how specific countries contribute to your revenue",
                        "Evaluate country-level sensitivity to discounts",
                        "Understand refund behavior by country",
                    ],
                },
                "dashboard_url": "",
                "slicers": ["ProductName", "Stores", "Date", "Sale Day Type", "Studio"],
            },
            {
                "id": "1a04b11d-139c-4d68-a6bb-9221b32541fb",
                "type": "folder",
                "icon": "chart-bubble",
                "display_name": "Details",
                "description": {
                    "title": "Details by portals, products and regions.",
                    "description": "A closer look into your performance focused on portals and titles.",
                    "features": [
                        "See the relative value of different portals over time by title",
                        "Drill into which portals and stores are most relevant in specific countries and regions",
                        "Track each title’s contribution to revenue and unit sales",
                        "See which regions and countries are most noteworthy for each title",
                    ],
                },
                "children": [
                    {
                        "id": "fe6f62ab-e93f-4766-8fd1-a302e2a13535",
                        "type": "powerbi",
                        "icon": "chart-bubble",
                        "display_name": "By portal",
                        "internal_name": "Details by Portal",
                        "pbix_name": "Details by",
                        "page_name": "ReportSectionb5e621a3a7b3db9b1936",
                        "visual_container_id": "e44cb7cb5bac408da842",
                        "description": {
                            "title": "Compare portals",
                            "description": "A closer look into your performance across all portals.",
                            "features": [
                                "See the relative value of different portals over time by title",
                                "Better understand promotional behavior on each portal",
                                "Drill into which portals are most relevant in specific countries and regions",
                            ],
                        },
                        "dashboard_url": "",
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Date",
                            "Selected Measure All",
                            "Sale Day Type",
                            "Studio",
                        ],
                    },
                    {
                        "id": "eabf7271-8f63-4047-9ac4-9b2db6bb300f",
                        "type": "powerbi",
                        "icon": "chart-bubble",
                        "display_name": "By store",
                        "internal_name": "Details by Store",
                        "pbix_name": "Details by",
                        "page_name": "ReportSectionc7be4332fbd92e944699",
                        "visual_container_id": "8a9253f1f14a79418332",
                        "description": {
                            "title": "Compare stores",
                            "description": "A closer look into your performance across all stores.",
                            "features": [
                                "See the relative value of different stores over time by title",
                                "Better understand promotional behavior on each store",
                                "Drill into which stores are most relevant in specific countries and regions",
                            ],
                        },
                        "dashboard_url": "",
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Date",
                            "Selected Measure All",
                            "Sale Day Type",
                            "Studio",
                        ],
                    },
                    {
                        "id": "2e66231e-1727-4c4a-8557-5812b1bd5fc6",
                        "type": "powerbi",
                        "icon": "chart-bubble",
                        "display_name": "By product",
                        "internal_name": "Details by Product",
                        "pbix_name": "Details by",
                        "page_name": "ReportSection974f2cc768db998dd94e",
                        "visual_container_id": "533d375d9cda2d8c81b5",
                        "description": {
                            "title": "Compare titles",
                            "description": "A closer look into the relative performance of each of your titles.",
                            "features": [
                                "Track each title’s contribution to revenue and unit sales",
                                "Monitor evolution of title mix over time",
                                "Understand how each title reacts to promotions",
                                "See which platforms are most relevant for each title",
                            ],
                        },
                        "dashboard_url": "",
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Date",
                            "Selected Measure All",
                            "Sale Day Type",
                            "Studio",
                        ],
                    },
                    {
                        "id": "d850f6c1-2d65-4bc5-b949-177e097403e3",
                        "type": "powerbi",
                        "icon": "chart-bubble",
                        "display_name": "By region",
                        "internal_name": "Details by Region",
                        "pbix_name": "Details by",
                        "page_name": "ReportSection38b25716b3407b50ff9f",
                        "visual_container_id": "d1fff83e4a4dd44215cb",
                        "description": {
                            "title": "Compare regions",
                            "description": "A closer look into your performance across all regions.",
                            "features": [
                                "Track each region’s contribution to revenue and unit sales",
                                "See which regions are most noteworthy for each title",
                                "Check promotional intensity per region",
                            ],
                        },
                        "dashboard_url": "",
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Date",
                            "Selected Measure All",
                            "Sale Day Type",
                            "Studio",
                        ],
                    },
                    {
                        "id": "48306f2b-f20f-4de7-ade0-3768d1e8b019",
                        "type": "powerbi",
                        "icon": "chart-bubble",
                        "display_name": "By country",
                        "internal_name": "Details by Country",
                        "pbix_name": "Details by",
                        "page_name": "ReportSection56b0474b24f15f907f88",
                        "visual_container_id": "cd9ed64d196e9900be51",
                        "description": {
                            "title": "Compare countries",
                            "description": "A closer look into your performance across all countries.",
                            "features": [
                                "Track contribution to revenue and unit sales across all countries",
                                "See which countries are most noteworthy for each title",
                                "Check promotional intensity per country",
                            ],
                        },
                        "dashboard_url": "",
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Date",
                            "Selected Measure All",
                            "Sale Day Type",
                            "Studio",
                        ],
                    },
                ],
            },
            {
                "id": "06d09acb-115c-4369-ad4e-ce2cddc0494d",
                "type": "folder",
                "icon": "ab-testing",
                "display_name": "Compare data",
                "description": {
                    "title": "Compare data",
                    "description": "A detailed view for comparing chosen titles by regions and performance between two time periods.",
                    "features": [
                        "See how your revenue and unit sales changed between set periods of time",
                        "Look for patterns and regional anomalies",
                        "Check the biggest drivers of differences between periods or titles",
                        "Identify outliers by title, country, and portals",
                    ],
                },
                "children": [
                    {
                        "id": "06729661-15c4-4cb9-8dcd-40dbe612eb20",
                        "type": "powerbi",
                        "icon": "ab-testing",
                        "display_name": "By periods",
                        "internal_name": "Compare Periods",
                        "pbix_name": "Compare Periods",
                        "page_name": "ReportSectionf513e7352f0c0f119c5b",
                        "visual_container_id": "23146ac8380b09d64950",
                        "description": {
                            "title": "Compare periods",
                            "description": "Compare title or catalog performance between two time periods.",
                            "features": [
                                "See how your revenue and unit sales changed between set periods of time",
                                "Identify the biggest drivers of differences between periods",
                                "Identify outliers by title, country, and platforms",
                            ],
                        },
                        "dashboard_url": "",
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Base Period",
                            "Second Period",
                            "Selected Measure All",
                            "Sale Day Type",
                            "Studio",
                        ],
                    },
                    {
                        "id": "edf1f648-e110-4ebc-99cf-e4ba1aa2ca46",
                        "type": "powerbi",
                        "icon": "ab-testing",
                        "display_name": "By regions",
                        "internal_name": "Compare Regions",
                        "pbix_name": "Compare Regions",
                        "page_name": "ReportSectionb5e621a3a7b3db9b1936",
                        "visual_container_id": "57d78400264241e2e7b6",
                        "description": {
                            "title": "Compare regions and countries",
                            "description": "Select two sets of regions and countries and compare your performance in them.",
                            "features": [
                                "Look for patterns and regional anomalies",
                                "Evaluate the result of your targeted campaigns or localization efforts",
                                "Compare sales in specific regions month by month and see if platforms perform similarly",
                            ],
                        },
                        "dashboard_url": "",
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Date",
                            "Base Region",
                            "Second Region",
                            "Sale Day Type",
                            "Studio",
                        ],
                    },
                ],
            },
            {
                "id": "2a53dda5-d365-47bf-a4bf-06d8dc8be80e",
                "type": "folder",
                "icon": "chart-box-plus-outline",
                "display_name": "Data Explorer",
                "description": {
                    "title": "Data Explorer",
                    "description": "Explore data, correlate different measures together, and create visuals on your own in this space.",
                    "features": [
                        "Explore data and create visuals on your own",
                        "Dive deep into data with interactive charts and graphs",
                        "Customize your visualizations to match your unique preferences",
                    ],
                },
                "children": [
                    {
                        "id": "b7188a6a-e436-4b58-983d-64152c632a17",
                        "type": "powerbi",
                        "icon": "chart-box-plus-outline",
                        "display_name": "Do-It-Yourself Chart",
                        "internal_name": "DIY",
                        "pbix_name": "DIY",
                        "page_name": "ReportSection05c5f627e3f15d9b903f",
                        "description": {
                            "title": "Do-It-Yourself",
                            "description": "Compare title or catalog performance between two time periods.",
                            "features": [
                                "Customize your visualizations to match your unique preferences and analytical requirements",
                                "Dive deeper into your data by interacting directly with charts and exploring different perspectives",
                            ],
                        },
                        "dashboard_url": "",
                        "slicers": [
                            "ProductName",
                            "Stores",
                            "Date",
                            "Sale Day Type",
                            "Studio",
                        ],
                    },
                    {
                        "id": "4100c7a2-da74-4d91-825e-81cfcb41fc95",
                        "type": "powerbi",
                        "icon": "chart-box-plus-outline",
                        "display_name": "AI Data Assistant",
                        "internal_name": "Data Assistant",
                        "pbix_name": "Data Assistant",
                        "page_name": "ReportSectionf7acd7b8573e0493a923",
                        "description": {
                            "title": "AI Data Assistant",
                            "description": "Explore data and create visuals yourself",
                            "features": [
                                "Simply ask questions in plain language to get instant insights",
                                "Dive deep into data with interactive charts and graphs",
                            ],
                        },
                        "dashboard_url": "",
                        "hide_slicers": True,
                    },
                ],
            },
            {"isDivider": True},
            {
                "id": "9097d83e-2871-4d66-a207-058dace97973",
                "icon": "chart-areaspline",
                "type": "folder",
                "display_name": "Discount analytics",
                "description": {
                    "title": "Discount analytics",
                    "description": "Analysis and comparison of your discounts on Steam and other portals.",
                    "features": [
                        "Evaluate your discounts and compare them with others",
                        "Identify unusual dips, peaks and other anomalies",
                        "Check the effectiveness of your discounts",
                    ],
                },
                "children": [
                    {
                        "id": "7d43685b-2c7d-475b-94c3-4e6118a46b49",
                        "type": "powerbi",
                        "icon": "chart-areaspline",
                        "display_name": "Steam discounts",
                        "internal_name": "Steam Discounts",
                        "pbix_name": "Discount Analysis",
                        "page_name": "ReportSection4dc6c3732dda6d904453",
                        "description": {
                            "title": "Steam discounts",
                            "description": "Analysis and comparison of your discounts on Steam.",
                            "features": [
                                "Evaluate your discounts and compare them with others",
                                "Identify strange dips and anomalies",
                                "Visualize the effectiveness of your discounts",
                            ],
                        },
                        "dashboard_url": "",
                        "minDate": "ago2y",
                        "limit_filters": {"platforms": ["Steam"]},
                        "disable_save": ["platforms"],
                        "slicers": ["Single_ProductName", "Date", "Studio"],
                    },
                    {
                        "id": "0e1dda8e-bd26-4dea-891a-053706d77bea",
                        "type": "powerbi",
                        "icon": "chart-areaspline",
                        "display_name": "Other discounts",
                        "internal_name": "Other Discounts",
                        "pbix_name": "Discount Analysis",
                        "page_name": "ReportSection6b9e36a32557cec54700",
                        "description": {
                            "title": "Discounts",
                            "description": "Analysis and comparison of your discounts.",
                            "features": [
                                "Evaluate your discounts and compare them with others",
                                "Identify strange dips and anomalies",
                                "Visualize the effectiveness of your discounts",
                            ],
                        },
                        "dashboard_url": "",
                        "minDate": "ago2y",
                        "slicers": [
                            "Single_ProductName",
                            "Single_Store",
                            "Date",
                            "Studio",
                        ],
                    },
                ],
            },
            {
                "id": "63f68c47-79a1-4bf8-b837-4ff898d3a449",
                "type": "powerbi",
                "icon": "filter-variant",
                "display_name": "Conversion metrics",
                "internal_name": "Conversion metrics",
                "pbix_name": "Steam Visibility",
                "page_name": "ReportSectionb5e621a3a7b3db9b1936",
                "visual_container_id": "b3e34ece36a796ec61cf",
                "description": {
                    "title": "Visibility and engagement rates",
                    "description": "A view with detailed information about impressions, visits, and engagement rates",
                    "features": [
                        "Dig deep into the sources and quality of your traffic",
                        "See how your engagement metrics change during sales, after you change your store art, or when you ship a big update",
                        "Better understand the causes behind specific sales over- and under-performing",
                        "Identify which sales got major homepage boosts, and which were driven by wishlist activations and top charts",
                    ],
                },
                "dashboard_url": "",
                "accepts_measures": ["Steam Visibility"],
                "limit_filters": {"platforms": ["Steam"]},
                "disable_save": ["platforms"],
                "slicers": ["ProductName", "Date", "Sale Day Type", "Studio"],
            },
            {
                "id": "c069714b-0fdb-416d-a145-ef473be8c9c2",
                "type": "powerbi",
                "icon": "trophy-outline",
                "display_name": "Top products",
                "internal_name": "Top Products",
                "pbix_name": "Top Products",
                "page_name": "ReportSection015a5759087d2bcc815b",
                "visual_container_id": "191997966853de58c006",
                "description": {
                    "title": "Your top products",
                    "description": "A simple, summarized view at your best selling games.",
                    "features": [
                        "See your top 10 products in a given country and region",
                        "Check the percentage of your best products in total sales",
                    ],
                },
                "dashboard_url": "",
                "slicers": [
                    "ProductName",
                    "Selected Measure All",
                    "Date",
                    "Stores",
                    "Sale Day Type",
                    "Studio",
                ],
            },
            {"isDivider": True},
            {
                "id": "f5147098-3f83-42c9-9fc8-ecdf027addd3",
                "type": "powerbi",
                "icon": "table-arrow-right",
                "display_name": "Export data",
                "internal_name": "Simple Data Export",
                "pbix_name": "Data Export",
                "page_name": "ReportSection4fd9d0cd5d9cf15114b8",
                "visual_container_id": "ad65dc1ebde39283a6b6",
                "description": {
                    "title": "Simple data export",
                    "description": "A simple view to download your most important metrics (like revenue, units sold, Steam wishlists and visibility) broken down by dimensions of your choice. ",
                    "features": [
                        "Select the data you are interested in and easily export it to a CSV file.",
                        "Extract your basic data easily, without having to set up direct database access.",
                    ],
                },
                "dashboard_url": "",
                "slicers": ["ProductName", "Date", "Stores", "Sale Day Type", "Studio"],
            },
            {
                "id": "ca8d8375-a104-4cb7-8880-45c425825236",
                "type": "powerbi",
                "icon": "handshake",
                "display_name": "Monthly analysis",
                "internal_name": "Monthly analysis",
                "pbix_name": "Monthly",
                "page_name": "ReportSectionf513e7352f0c0f119c5b",
                "description": {
                    "title": "Monthly and yearly comparison",
                    "description": "A simple view to check YOY and MOM data for titles and portals. ",
                    "features": [
                        "Check how the revenue and units values ​​have changed compared to the previous period and previous year's period.",
                        "Select the data you are interested in and easily export it to a CSV file.",
                    ],
                },
                "dashboard_url": "",
                "slicers": [
                    "ProductName",
                    "Date",
                    "Stores",
                    "Studio",
                    "Selected Measure All",
                ],
                "needs_permission": True,
            },
            {
                "id": "6c82635e-8b48-4865-9823-a9ccd04eaf2c",
                "type": "powerbi",
                "icon": "chart-bar-stacked",
                "display_name": "Dashboard",
                "internal_name": "Dashboard",
                "pbix_name": "Dashboard Mobile",
                "page_name": "ReportSectiond4e03815e262394d0e4a",
                "visual_container_id": "6ccabd080adb09e2b7e2",
                "description": {
                    "title": "Revenue overview",
                    "description": "Overview of your revenue and other relevant metrics.",
                    "features": [
                        "See performance over time by title, platform and geography",
                        "Identify strange dips and anomalies",
                    ],
                },
                "dashboard_url": "",
                "accepts_measures": ["Revenue"],
                "hidden": True,
            },
        ],
    }


@pytest.fixture
def dev_env_config():
    class FakeConfig:
        ENV = "dev"
        ENV_PREFIX = ""

    config = FakeConfig()

    def override_config():
        yield config

    app.dependency_overrides[get_config] = override_config
    yield config
    app.dependency_overrides = {}


@pytest.fixture
def asign_capacity_to_dataset_template(responses_mock: responses.RequestsMock):
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/dataset_template-4e18-aea3-4cb4a3a50b48/AssignToCapacity",
        match=[
            matchers.json_params_matcher(
                {"capacityId": "capacity-default-c13d-451b-af5f-ed0c4664"}
            )
        ],
    )


@pytest.fixture
def asign_capacity_to_visuals(responses_mock: responses.RequestsMock):
    responses_mock.post(
        "https://api.powerbi.com/v1.0/myorg/groups/visuals-4e18-aea3-4cb4a3a50b48/AssignToCapacity",
        match=[
            matchers.json_params_matcher(
                {"capacityId": "capacity-default-c13d-451b-af5f-ed0c4664"}
            )
        ],
    )


@freeze_time("2022-11-07 10:00")
def test_create_new_version_with_new_dataset(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
    power_bi_api,
    create_not_existing_workspace_dataset_template,
    asign_capacity_to_dataset_template,
    asign_capacity_to_visuals,
    deploy_dataset,
    get_uploaded_dataset_id_with_retry,
    create_not_existing_visuals_workspace,
    deploy_first_visual_with_replaced_connection_string,
    deploy_second_visual_with_replaced_connection_string,
    get_deployed_reports,
    dev_env_config,
    expected_desktop_definition,
    models_client: ContainerClient,
):
    capacity_factory(
        id="capacity-default-c13d-451b-af5f-ed0c4664",
        name="embed_capacity_name",
        is_default=True,
    )
    dataset_pbix = b"SOME_DATASET"

    first_visual_pbix = io.BytesIO()
    with ZipFile(first_visual_pbix, "w", compression=ZIP_DEFLATED) as zip_file:
        zip_file.writestr("charts", "Dashboards")
        zip_file.writestr("Connections", json.dumps({"version": 3}))

    second_visual_pbix = io.BytesIO()
    with ZipFile(second_visual_pbix, "w", compression=ZIP_DEFLATED) as zip_file:
        zip_file.writestr("charts", "Sales And Discounts")
        zip_file.writestr("Connections", json.dumps({"version": 3}))

    files = [
        ("dataset", ("Dataset.pbix", dataset_pbix)),
        ("visuals", ("Dashboards.pbix", first_visual_pbix.getvalue())),
        ("visuals", ("Sales And Discounts.pbix", second_visual_pbix.getvalue())),
    ]

    response = client.post(
        "/version/2.2.0",
        files=files,
        headers=authorization_header,
    )

    assert response.status_code == HTTP_200_OK

    all_versions = db_session.query(_DBVersion).all()
    assert len(all_versions) == 1, all_versions
    created_version = all_versions[0]

    assert created_version.id == "2.2.0"
    assert created_version.shard_version == "2.2.0"
    assert (
        created_version.shard_template_workspace_id
        == "dataset_template-4e18-aea3-4cb4a3a50b48"
    )
    assert created_version.visuals_workspace_id == "visuals-4e18-aea3-4cb4a3a50b48"
    assert (
        created_version.visuals_dataset_id == "dataset_template_8037-4d0c-896e-a46fb27"
    )
    assert created_version.visuals_reports_ids == [
        "dashboards-9a9e3717-e1a2-4c3f-9cf2-97101e3af4aa",
        "sales-8501b756-a4b5-4723-8a4a-5363bb8aa4bd",
    ]
    assert created_version.visuals_desktop_definitions == expected_desktop_definition
    assert created_version.is_active is True
    assert created_version.is_default is False

    assert len(db_session.query(_DBRelease).all()) == 0

    all_blobs = [x for x in models_client.list_blob_names()]
    assert all_blobs == ["2.2.0"]
    assert models_client.download_blob("2.2.0").readall() == b"SOME_DATASET"


@freeze_time("2022-11-07 10:00")
def test_create_new_version_with_old_dataset(
    db_session: Session,
    client,
    authorization_header,
    capacity_factory,
    version_factory,
    power_bi_api,
    create_not_existing_visuals_workspace,
    asign_capacity_to_visuals,
    deploy_first_visual_with_replaced_connection_string,
    deploy_second_visual_with_replaced_connection_string,
    get_deployed_reports,
    dev_env_config,
    expected_desktop_definition,
):
    capacity_factory(
        id="capacity-default-c13d-451b-af5f-ed0c4664",
        name="embed_capacity_name",
        is_default=True,
    )
    version_factory(
        id="2.1.0",
        shard_version="2.1.0",
        visuals_dataset_id="dataset_template_8037-4d0c-896e-a46fb27",
    )

    first_visual_pbix = io.BytesIO()
    with ZipFile(first_visual_pbix, "w", compression=ZIP_DEFLATED) as zip_file:
        zip_file.writestr("charts", "Dashboards")
        zip_file.writestr("Connections", json.dumps({"version": 3}))

    second_visual_pbix = io.BytesIO()
    with ZipFile(second_visual_pbix, "w", compression=ZIP_DEFLATED) as zip_file:
        zip_file.writestr("charts", "Sales And Discounts")
        zip_file.writestr("Connections", json.dumps({"version": 3}))

    files = [
        ("visuals", ("Dashboards.pbix", first_visual_pbix.getvalue())),
        ("visuals", ("Sales And Discounts.pbix", second_visual_pbix.getvalue())),
    ]

    response = client.post(
        "/version/2.2.0",
        files=files,
        data={"shard_version": "2.1.0"},
        headers=authorization_header,
    )

    assert response.status_code == HTTP_200_OK

    all_versions = db_session.query(_DBVersion).all()
    assert len(all_versions) == 2, all_versions
    created_version = all_versions[1]

    assert created_version.id == "2.2.0"
    assert created_version.shard_version == "2.1.0"
    assert (
        created_version.shard_template_workspace_id
        == "shard-template-workspace-bf7d-13880a5c3c"
    )
    assert created_version.visuals_workspace_id == "visuals-4e18-aea3-4cb4a3a50b48"
    assert (
        created_version.visuals_dataset_id == "dataset_template_8037-4d0c-896e-a46fb27"
    )
    assert created_version.visuals_reports_ids == [
        "dashboards-9a9e3717-e1a2-4c3f-9cf2-97101e3af4aa",
        "sales-8501b756-a4b5-4723-8a4a-5363bb8aa4bd",
    ]
    assert created_version.visuals_desktop_definitions == expected_desktop_definition
    assert created_version.is_active is True
    assert created_version.is_default is False

    assert len(db_session.query(_DBRelease).all()) == 0


def test_dataset_file_is_required_if_shard_version_is_updated(
    client,
    authorization_header,
):
    response = client.post(
        "/version/2.2.0",
        files=[
            ("visuals", ("Dashboards.pbix", b"fake_dasboard")),
            ("visuals", ("Sales And Discounts.pbix", b"fake_sales")),
        ],
        data={"shard_version": "2.2.0"},
        headers=authorization_header,
    )

    assert response.status_code == HTTP_422_UNPROCESSABLE_ENTITY
    details = response.json()
    del details["detail"][0]["url"]
    assert details == {
        "detail": [
            {
                "input": None,
                "loc": ["__root__", "dataset"],
                "msg": "Field required",
                "type": "missing",
            }
        ]
    }


@freeze_time("2022-11-07 10:00")
def test_dev_version_can_be_overriden(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    capacity_factory,
    version_factory,
    permission_set_factory,
    power_bi_api,
    create_not_existing_dev_workspace_dataset_template,
    asign_capacity_to_dataset_template,
    asign_capacity_to_visuals,
    deploy_dataset,
    get_uploaded_dataset_id_with_retry,
    create_not_existing_dev_visuals_workspace,
    deploy_first_visual_with_replaced_connection_string,
    deploy_second_visual_with_replaced_connection_string,
    get_deployed_reports,
    dev_env_config,
    expected_desktop_definition,
):
    capacity_factory(
        id="capacity-default-c13d-451b-af5f-ed0c4664",
        name="embed_capacity_name",
        is_default=True,
    )
    version_factory(
        id="2.2.0_dev",
        shard_version="2.2.0_dev",
    )

    permission_set_1 = permission_set_factory()
    permission_set_2 = permission_set_factory(
        permission_set=[{"studio_id": 2, "product_name": None}]
    )

    profile_factory(
        studio_id=1,
        active_version_id="2.2.0_dev",
        permission_set_uuid=permission_set_1.uuid,
    )
    profile_factory(
        studio_id=2,
        profile_id="profile_studio_2",
        active_version_id="2.2.0_dev",
        permission_set_uuid=permission_set_2.uuid,
    )
    profile_factory(
        studio_id=3,
        profile_id="profile_studio_3",
        permission_set_uuid=permission_set_1.uuid,
    )

    dataset_pbix = b"SOME_DATASET"

    first_visual_pbix = io.BytesIO()
    with ZipFile(first_visual_pbix, mode="w", compression=ZIP_DEFLATED) as zip_file:
        zip_file.writestr("charts", "Dashboards")
        zip_file.writestr("Connections", json.dumps({"version": 3}))

    second_visual_pbix = io.BytesIO()
    with ZipFile(second_visual_pbix, mode="w", compression=ZIP_DEFLATED) as zip_file:
        zip_file.writestr("charts", "Sales And Discounts")
        zip_file.writestr("Connections", json.dumps({"version": 3}))

    files = [
        ("dataset", ("Dataset.pbix", dataset_pbix)),
        ("visuals", ("Dashboards.pbix", first_visual_pbix.getvalue())),
        ("visuals", ("Sales And Discounts.pbix", second_visual_pbix.getvalue())),
    ]

    response = client.post(
        "/version/2.2.0_dev",
        files=files,
        headers=authorization_header,
    )

    assert response.status_code == HTTP_200_OK

    all_versions = db_session.query(_DBVersion).all()
    assert len(all_versions) == 1, all_versions
    created_version = all_versions[0]

    assert created_version.id == "2.2.0_dev"
    assert created_version.shard_version == "2.2.0_dev"

    all_releases = sorted(db_session.query(_DBRelease).all(), key=lambda r: r.studio_id)
    assert all_releases[0].studio_id == 1
    assert all_releases[0].version_id == "2.2.0_dev"
    assert all_releases[0].permission_set_uuid == permission_set_1.uuid

    assert all_releases[1].studio_id == 2
    assert all_releases[1].version_id == "2.2.0_dev"
    assert all_releases[1].permission_set_uuid == permission_set_2.uuid

    assert len(all_releases) == 2, all_releases


@freeze_time("2022-11-07 10:00")
def test_undone_release_is_marked_as_outdated(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    capacity_factory,
    version_factory,
    permission_set_factory,
    release_factory,
    power_bi_api,
    create_not_existing_dev_workspace_dataset_template,
    asign_capacity_to_dataset_template,
    asign_capacity_to_visuals,
    deploy_dataset,
    get_uploaded_dataset_id_with_retry,
    create_not_existing_dev_visuals_workspace,
    deploy_first_visual_with_replaced_connection_string,
    deploy_second_visual_with_replaced_connection_string,
    get_deployed_reports,
    dev_env_config,
    expected_desktop_definition,
):
    capacity_factory(
        id="capacity-default-c13d-451b-af5f-ed0c4664",
        name="embed_capacity_name",
        is_default=True,
    )
    version_factory(
        id="2.2.0_dev",
        shard_version="2.2.0_dev",
    )

    permission_set_1 = permission_set_factory()

    profile_factory(
        studio_id=1,
        active_version_id="2.2.0_dev",
        permission_set_uuid=permission_set_1.uuid,
    )

    release_factory(
        studio_id=1,
        version_id="2.0.4",
        permission_set_uuid="eeeeeee1-7d79-4403-83e9-916b65129739",
        creation_timestamp=datetime(2022, 11, 7, 9, 55),
    )

    dataset_pbix = b"SOME_DATASET"

    first_visual_pbix = io.BytesIO()
    with ZipFile(first_visual_pbix, mode="w", compression=ZIP_DEFLATED) as zip_file:
        zip_file.writestr("charts", "Dashboards")
        zip_file.writestr("Connections", json.dumps({"version": 3}))

    second_visual_pbix = io.BytesIO()
    with ZipFile(second_visual_pbix, mode="w", compression=ZIP_DEFLATED) as zip_file:
        zip_file.writestr("charts", "Sales And Discounts")
        zip_file.writestr("Connections", json.dumps({"version": 3}))

    files = [
        ("dataset", ("Dataset.pbix", dataset_pbix)),
        ("visuals", ("Dashboards.pbix", first_visual_pbix.getvalue())),
        ("visuals", ("Sales And Discounts.pbix", second_visual_pbix.getvalue())),
    ]

    response = client.post(
        "/version/2.2.0_dev",
        files=files,
        headers=authorization_header,
    )

    assert response.status_code == HTTP_200_OK

    all_releases = sorted(
        db_session.query(_DBRelease).all(), key=lambda r: r.version_id
    )
    assert all_releases[0].studio_id == 1
    assert all_releases[0].version_id == "2.0.4"
    assert all_releases[0].permission_set_uuid == permission_set_1.uuid
    assert all_releases[0].status == ReleaseStatus.OUTDATED

    assert all_releases[1].studio_id == 1
    assert all_releases[1].version_id == "2.2.0_dev"
    assert all_releases[1].permission_set_uuid == permission_set_1.uuid
    assert all_releases[1].status == ReleaseStatus.REQUESTED

    assert len(all_releases) == 2, all_releases
